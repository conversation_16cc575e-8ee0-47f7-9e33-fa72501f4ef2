
/* Social Container */
.social-container {
  position: fixed;
  top: 39rem;
  left: 80rem;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Base Social Button Styles */
.social-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.social-btn-facebook{
    position: fixed;
    left: 70rem;
    bottom: 2rem;
}

.social-btn-twitter{
    position: fixed;
    left: 75rem;
    bottom: 2rem;
}

.social-btn-instagram{
    position: fixed;
    left: 80rem;
    bottom: 2rem;
}

.social-btn-snapchat{
    position: fixed;
    left: 85rem;
    bottom: 2rem;
}

.svgContainer {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  backdrop-filter: blur(0px);
  border-radius: 15px;
  transition: all 0.3s ease;
  z-index: 3;
  position: relative;
}

.BG {
  position: absolute;
  content: "";
  width: 40px;
  height: 40px;
  z-index: 2;
  border-radius: 15px;
  pointer-events: none;
  transition: all 0.3s ease;
  top: 0;
  left: 0;
}

/* Dark Background Colors for Each Platform */
.facebook-bg {
  background: #2c2c2c00;
}

.twitter-bg {
  background: #2c2c2c00;
}

.instagram-bg {
  background: #2c2c2c00;
}

.snapchat-bg {
  background: #2c2c2c00;
}

/* Hover Effects - Change to Brand Colors */
.social-btn-facebook:hover .facebook-bg {
  background: #1877f2;
  transform: scale(1.05);
}

.social-btn-twitter:hover .twitter-bg {
  background: #000000;
  transform: scale(1.05);
}

.social-btn-instagram:hover .instagram-bg {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  transform: scale(1.05);
}

.social-btn-snapchat:hover .snapchat-bg {
  background: #fffc00;
  transform: scale(1.05);
}

/* SVG Icon Hover Effects */
.social-btn:hover .svgContainer {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
}

/* Special case for Snapchat - make icon black on yellow background */
.snapchat:hover svg {
  fill: #000000;
}
