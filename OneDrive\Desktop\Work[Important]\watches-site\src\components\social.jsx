import { useState } from 'react';
import '../styles/social.css'

function Social() {

    return (
        <div className="social-container">
            {/* Facebook */}
            <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" className="social-btn-facebook">
                <span className="svgContainer">
                    <svg
                        viewBox="0 0 320 512"
                        fill="white"
                        height="1.6em"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z"
                        />
                    </svg>
                </span>
                <span className="BG facebook-bg"></span>
            </a>

            {/* X (Twitter) */}
            <a href="https://x.com" target="_blank" rel="noopener noreferrer" className="social-btn-twitter">
                <span className="svgContainer">
                    <svg
                        viewBox="0 0 512 512"
                        fill="white"
                        height="1.6em"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z"
                        />
                    </svg>
                </span>
                <span className="BG twitter-bg"></span>
            </a>

            {/* Instagram */}
            <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" className="social-btn-instagram">
                <span className="svgContainer">
                    <svg
                        viewBox="0 0 448 512"
                        fill="white"
                        height="1.6em"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"
                        />
                    </svg>
                </span>
                <span className="BG instagram-bg"></span>
            </a>

            {/* Snapchat */}
            <a href="https://snapchat.com" target="_blank" rel="noopener noreferrer" className="social-btn-snapchat">
                <span className="svgContainer">
                    <svg
                        viewBox="0 0 512 512"
                        fill="white"
                        height="1.6em"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M496.926,366.6c-3.373-9.176-9.8-14.086-17.112-18.153-1.376-.806-2.641-1.451-3.72-1.947-2.182-1.128-4.414-2.22-6.634-3.373-22.8-12.09-40.609-27.341-52.959-45.42a102.889,102.889,0,0,1-9.089-16.12c-1.054-3.013-1-4.724-.248-6.287a10.221,10.221,0,0,1,2.914-3.038c3.918-2.591,7.96-5.22,10.7-6.993,4.885-3.162,8.754-5.667,11.246-7.44,9.362-6.547,15.909-13.5,20-21.278a42.371,42.371,0,0,0,2.1-35.191c-6.2-16.318-21.613-26.449-40.287-26.449a55.543,55.543,0,0,0-11.718,1.24c-1.029.224-2.059.459-3.063.72.174-11.16-.074-22.94-1.066-34.534C394.315,123.5,337.763,87.5,256.005,87.5S117.7,123.5,114.681,141.623c-.992,11.594-1.24,23.374-1.066,34.534-1,.261-2.034-.5-3.063-.72a55.543,55.543,0,0,0-11.718-1.24c-18.674,0-34.087,10.131-40.287,26.449a42.371,42.371,0,0,0,2.1,35.191c4.094,7.781,10.641,14.731,20,21.278,2.492,1.773,6.361,4.278,11.246,7.44,2.735,1.773,6.777,4.4,10.7,6.993a10.221,10.221,0,0,1,2.914,3.038c.751,1.563.806,3.274-.248,6.287a102.889,102.889,0,0,1-9.089,16.12c-12.35,18.079-30.159,33.33-52.959,45.42-2.22,1.153-4.452,2.245-6.634,3.373-1.079.5-2.344,1.141-3.72,1.947-7.314,4.067-13.739,8.977-17.112,18.153-3.373,9.176-2.9,19.286,1.218,27.64C31.6,407.311,54.1,424.5,256.005,424.5s224.4-17.189,239.7-30.26C500.829,385.888,500.3,375.778,496.926,366.6Z"
                        />
                    </svg>
                </span>
                <span className="BG snapchat-bg"></span>
            </a>
        </div>
    );
}

export default Social;