import { useState } from 'react'
import '../styles/middle.css'

import Image1 from '../assets/images/image-1.jpeg'
import Image2 from '../assets/images/image-2.jpeg'
import Image3 from '../assets/images/image-3.jpeg'
import Image4 from '../assets/images/image-4.jpeg'
import Video1 from '../assets/video/video-1.mp4'

function Middle() {
  const [count, setCount] = useState(0);

  return (
    <>

      <div className="parent">
        <div className="div1">
          <img src={Image1} alt="Image 1" className="Image1" />
        </div>
        <div className="div2">
          <img src={Image2} alt="Image 2" className="Image2" />
        </div>
        <div className="div3">
          <video src={Video1} autoPlay loop muted className="Video1" />
        </div>
        <div className="div4">
          <img src={Image3} alt="Image 3" className="Image3" />
        </div>
        <div className="div5">
          <img src={Image4} alt="Image 4" className="Image4" />
        </div>
      </div>

      <div className="marquee-tilted-wrapper">
        <div className="marquee-wrapper">
          <div className="marquee-content">
            <span>🔥 Exclusive Offers & Discounts 🔥</span>
            <span>🔥 Limited Time Deals 🔥</span>
            <span>🔥 Save Big Today 🔥</span>
            <span>🔥 Shop Now & Save 🔥</span>
            <span>🔥 Special Festive Discounts 🔥</span>
            <span>🔥 Exclusive Offers & Discounts 🔥</span>
            <span>🔥 Limited Time Deals 🔥</span>
            <span>🔥 Save Big Today 🔥</span>
            <span>🔥 Shop Now & Save 🔥</span>
            <span>🔥 Special Festive Discounts 🔥</span>
          </div>
        </div>
      </div>

    </>
  );
}

export default Middle;
