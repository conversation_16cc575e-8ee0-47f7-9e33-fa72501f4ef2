import React, { useState } from 'react';
import '../styles/watches.css';

// Import watch images
import watch1 from '../assets/images/watch-1.png';
import watch2 from '../assets/images/watch-2.png';
import watch3 from '../assets/images/watch-3.png';
import watch4 from '../assets/images/watch-4.png';
import watch5 from '../assets/images/watch-5.png';

const Watches = () => {
  const images = [watch1, watch2, watch3, watch4, watch5];
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const handleImageChange = (newIndex) => {
    if (isTransitioning || newIndex === currentImageIndex) return;

    setIsTransitioning(true);

    // After fade out completes, change image and fade in
    setTimeout(() => {
      setCurrentImageIndex(newIndex);
      setTimeout(() => {
        setIsTransitioning(false);
      }, 50); // Small delay to ensure image has changed before fading in
    }, 300); // Match CSS transition duration
  };

  const goToPrevious = () => {
    const newIndex = currentImageIndex === 0 ? images.length - 1 : currentImageIndex - 1;
    handleImageChange(newIndex);
  };

  const goToNext = () => {
    const newIndex = currentImageIndex === images.length - 1 ? 0 : currentImageIndex + 1;
    handleImageChange(newIndex);
  };

  return (
    <div className="carousel-container">
      <div className="carousel-main">
        {/* Previous Button */}
        <button
          className="carousel-arrow carousel-arrow-left"
          onClick={goToPrevious}
          disabled={isTransitioning}
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>

        {/* Main Image Display */}
        <div className="carousel-image-container">
          <img
            src={images[currentImageIndex]}
            alt={`Watch ${currentImageIndex + 1}`}
            className={`carousel-main-image ${isTransitioning ? 'fade-out' : 'fade-in'}`}
          />
        </div>

        {/* Next Button */}
        <button
          className="carousel-arrow carousel-arrow-right"
          onClick={goToNext}
          disabled={isTransitioning}
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>
    </div>
  );
};

export default Watches;