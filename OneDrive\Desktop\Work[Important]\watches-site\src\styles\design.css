@font-face {
    font-family: 'Boldivia';
    src: url('../src/assets/fonts/boldovia.regular.ttf') format('truetype');
    font-style: normal;
    font-weight: normal;
}

#s {

    text-align: center;
    margin-top: -9rem;
    z-index: 10;
    color: #785a98;
    font-style: bold;
    font-size: 700px;
    font-family: 'Boldivia';
    /* Make sure it's loaded correctly */
    font-weight: 800;
    overflow-wrap: break-word;
    /* ✅ modern alternative to word-wrap */

}

.design2 {
    height: 300px;
    width: 300px;
    margin-left: -8rem;
    margin-top: -65rem;
    background-color: transparent;
    position: relative;
    border: 70px solid #dbdbdb;
    border-radius: 50%;
}

.design3 {
    width: 100px;
    height: 50px;
    color: black;
    font-family: 'boldivia';
    font-size: 2rem;
    position: relative;
    margin-top: -16rem;
    margin-left: 2rem;
    cursor: pointer;
}

.design4 {
    width: 100px;
    height: 50px;
    color: black;
    font-style: normal;
    font-family: 'boldivia' Arial, Helvetica, sans-serif;
    font-size: 1.2rem;
    position: relative;
    margin-left: 15.9rem;
    margin-top: -3rem;
    cursor: pointer;
}

hr {
    border: none;
    height: 1px;
    background-color: rgb(0, 0, 0);
    width: 100%;
    margin: 4px 0;
}

#hr-1 {
    width: 100px;
    height: 2px;
    position: relative;
    left: 31.5rem;
    bottom: 1rem;
}

#hr-2 {
    width: 100px;
    height: 1.5px;
    position: relative;
    background-color: rgb(185, 185, 185);
    left: 18.4rem;
    bottom: 1.1rem;
}

#hr-3 {
    width: 100px;
    height: 1.5px;
    position: relative;
    margin-left: 3.59rem;
    margin-top: -16px;
}

#hr-4 {
    width: 100px;
    height: 1.5px;
    background-color: rgb(185, 185, 185);
    position: relative;
    right: 7.269rem;
    bottom: 1rem;
    ;
}


.design5 {
    color: black;
    text-align: center;
    font-style: normal;
    font-size: 1.2rem;
    font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
    position: relative;
    margin-right: 36rem;
    margin-top: 5rem;
}

.design6 {
    color: rgb(185, 185, 185);
    font-style: normal;
    text-align: center;
    font-size: 1.2rem;
    font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
    position: relative;
    bottom: 2.5rem;
    margin-left: 40rem;
}

.design7 {

    color: black;
    font-style: normal;
    font-size: 1.2rem;
    font-family: Arial, Helvetica, sans-serif;
    position: relative;
    margin-left: 26.5rem;
    margin-top: 14.5rem;
}

.design8 {
    color: rgb(185, 185, 185);
    font-style: normal;
    font-size: 1.2rem;
    font-family: Arial, Helvetica, sans-serif;
    position: relative;
    left: 65.5rem;
    bottom: .8rem;
}

.center-design{
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    position: relative;
    text-align: center;
}

.design9 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: relative;
    right: 14rem;
    bottom: 20rem;

}

.design10 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: relative;
    right: 13rem;
    bottom: 20rem;

}

.design11 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: relative;
    right: 10rem;
    bottom: 20rem;

}

.design12 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: relative;
    left: 11rem;
    bottom: 20rem;

}

.design13 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: relative;
    left: 14rem;
    bottom: 20rem;

}

.design14 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: relative;
    left: 16rem;
    bottom: 20rem;

}

.design15 {
    width: 100px;
    height: 50px;
    color: rgb(190, 190, 190);
    font-style: normal;
    font-family: 'boldivia' Arial, Helvetica, sans-serif;
    font-size: 1rem;
    position: relative;
    left: 65rem;
    bottom: 37rem;
    cursor: pointer;
}

.design16 {
    width: 100px;
    height: 50px;
    color: rgb(190, 190, 190);
    font-style: normal;
    font-family: 'boldivia' Arial, Helvetica, sans-serif;
    font-size: 1rem;
    position: relative;
    left: 71.5rem;
    bottom: 40.2rem;
    cursor: pointer;
}

.design17 {
    width: 100px;
    height: 50px;
    color: rgb(190, 190, 190);
    font-style: normal;
    font-family: 'boldivia' Arial, Helvetica, sans-serif;
    font-size: 1rem;
    position: relative;
    left: 83rem;
    bottom: 43.5rem;
    cursor: pointer;

}

.blinking-down-arrow {
  animation: bounce 1.5s infinite;
  position: relative;
  bottom:10rem;
  left: 46rem;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(10px); }
}
