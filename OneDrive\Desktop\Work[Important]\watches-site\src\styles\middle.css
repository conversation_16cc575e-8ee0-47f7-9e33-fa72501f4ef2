.middle-background {
  position: relative;
  width: 100%;
  height: 100%;
  left: 0;
  z-index: 0;
  background: rgb(73, 73, 73);
  min-height: 100vh;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', '<PERSON>xygen',
    'Ubuntu', '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}


.parent {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-auto-rows: minmax(200px, auto);
  gap: 20px;
  padding: 20px;
  background-color: #fdf4ee;
  max-height: 500px;
}

/* Grid Item Sizes to Match Screenshot */
.div1 {
  grid-row: span 2;
}

.div2 {
  grid-column: 2;
  grid-row: 1;
}

.div3 {
  grid-column: 2;
  grid-row: 2;
}

.div4 {
  grid-column: 3;
  grid-row: 1;
}

.div5 {
  grid-column: 3;
  grid-row: 2;
}

/* Optional: Media Styling */
img, video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}


.marquee-wrapper {
  width: 110%;
  overflow: hidden;
  background-color: #fff8f0;
  padding: 10px 0;
}

.marquee-tilted-wrapper {
  overflow: hidden;
  width: 100%;
  padding: 10px 0;
  background-color: #fff8f0;
  margin: 20px 0;
}

.marquee-content {
  display: inline-block;
  white-space: nowrap;
  animation: scroll-loop 20s linear infinite;
}

.marquee-content span {
  display: inline-block;
  margin-right: 4rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: #d13f3f;
  font-family: sans-serif;
}

@keyframes scroll-loop {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-100%);
  }
}



